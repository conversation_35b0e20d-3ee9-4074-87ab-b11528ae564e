# 统一文档应答结构

## 1 文档结构描述

- **结构化数据**：文档按\[文档->章节->段落->图文控件]多级结构组织
- **类型化设计**：各级结构统一(标题\类型\样式等),以便定向提取加工
- **扩展化能力**：多种图文控件易于复用扩展,亦可定义更多控件种类

## 2 文档结构总览

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "title": "文档主标题",
    "subtitle": "文档副标题",
    "sections": [
      {
        "type": "章节类型枚举值",
        "title": "章节标题",
        "paragraphs": [
          {
            "title": "段落文本内容",
            "widgets": [
              {
                "type": "控件类型",
                "title": "标题",
                "style": "样式",
                "xxx": {
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 2.1 文档级结构

| 字段       | 类型     | 必填 | 说明           |
|----------|--------|----|--------------|
| title    | string | 是  | 文档主标题        |
| subtitle | string | 是  | 文档副标题        |
| sections | array  | 是  | 章节对象数组（至少1个） |

### 2.2 章节级结构

| 字段         | 类型     | 必填 | 说明                                                                                                                                   |
|------------|--------|----|--------------------------------------------------------------------------------------------------------------------------------------|
| type       | string | 是  | 章节类型：(以下为示例,可随意定义)<br>`POLICY_OVERVIEW`政策概览<br>`TAX_COMPARISON`税费对比<br>`MARKET_DATA`市场数据<br>`CASE_STUDY`案例研究<br>`HOT_PROPERTIES`热门房产 |
| title      | string | 是  | 章节标题(文档第一个章节的标题可为空)                                                                                                                  |
| paragraphs | array  | 是  | 段落对象数组（至少1个）                                                                                                                         |

### 2.3 段落级结构

| 字段      | 类型     | 必填 | 说明              |
|---------|--------|----|-----------------|
| title   | string | 否  | 段落标题            |
| widgets | array  | 否  | 图文控件对象数组（最多10个） |

### 2.4 图文控件结构

| 字段    | 类型           | 必填 | 说明                                                                                                           |
|-------|--------------|----|--------------------------------------------------------------------------------------------------------------|
| type  | string       | 是  | 控件类型：<br>`TEXT`纯文本<br>`TABLE`表格<br>`BAR_CHART`柱状图<br>`LINE_CHART`折线图<br>`PIE_CHART`饼图<br>`PROPERTY_CARD`房产卡片 |
| title | string       | 否  | 段落标题                                                                                                         |
| style | string       | 否  | 样式风格(枚举待定)                                                                                                   |
| xxx   | object/array | 是  | 控件数据                                                                                                         |

---

## 3. 图文控件数据规范

### 3.1 文本(TEXT)

```json
{
  "type": "TEXT",
  "style": "PLAIN/BLOCK/HIGH_LIGHT",
  "title": "标题",
  "content": "文本内容"
}
```

| 字段      | 类型     | 必填 | 说明                                          |
|---------|--------|----|---------------------------------------------|
| type    | string | 否  | 固定值`TEXT`                                   |
| style   | string | 否  | 样式(`PLAIN` 无修饰,`BLOCK` 文本框,`HIGH_LIGHT` 高亮) |
| title   | string | 否  | 标题                                          |
| content | string | 否  | 文本内容                                        |

### 3.1 列表(LIST)

```json
{
  "type": "LIST",
  "style": "SERIAL/ITEM",
  "title": "标题",
  "rows": [
    {
      "title": "标题",
      "content": "内容"
    }
  ]
}
```

| 字段      | 类型     | 必填 | 说明                         |
|---------|--------|----|----------------------------|
| type    | string | 否  | 固定值`TEXT`                  |
| style   | string | 否  | 样式(`SERIAL` 序号,`ITEM` 无序项) |
| title   | string | 否  | 标题                         |
| content | string | 否  | 文本内容                       |

### 3.3 表格(TABLE)

```json
{
  "type": "TABLE",
  "title": "表格标题",
  "columns": [
    "列标题1",
    "列标题2"
  ],
  "rows": [
    {
      "cells": {
        "列标题1": {
          "type": "TEXT/IMAGE/PROGRESS_BAR",
          "value": "单元格值",
          "recommended": true
        }
      }
    }
  ]
}
```

| 字段             | 类型      | 必填 | 说明                                                            |
|----------------|---------|----|---------------------------------------------------------------|
| type           | string  | 否  | 固定值`TABLE`                                                    |
| title          | string  | 否  | 表格标题                                                          |
| style          | string  | 否  | 样式风格(枚举待定)                                                    |
| columns        | array   | 是  | 列标题数组（至少2列）                                                   |
| rows           | array   | 是  | 行数据数组（至少1行）                                                   |
| ∟ cells        | object  | 是  | 单元格对象，键为列标题                                                   |
| ∟∟ type        | string  | 是  | 数据类型：<br>`TEXT`文本<br>`IMAGE`图片URL<br>`PROGRESS_BAR`进度值(0-100) |
| ∟∟ value       | any     | 是  | 单元格值                                                          |
| ∟∟ recommended | boolean | 否  | 是否推荐该选项（仅比较列有效）                                               |

### 3.4 柱状图/折线图 (BAR_CHART/LINE_CHART)

```json
{
  "type": "BAR_CHART/LINE_CHART",
  "title": "市场数据趋势",
  "x_axis": [
    "2024-07",
    "2024-08"
  ],
  "series": [
    {
      "name": "新增挂牌",
      "data": [
        11032,
        11567
      ]
    },
    {
      "name": "成交量",
      "data": [
        2015,
        1987
      ]
    }
  ]
}
```

| 字段     | 类型     | 必填 | 说明                                   |
|--------|--------|----|--------------------------------------|
| type   | string | 是  | 类型(`BAR_CHART` 柱状图,`LINE_CHART` 折线图) |
| title  | string | 是  | 图表标题                                 |
| x_axis | array  | 是  | X轴类别标签数组                             |
| series | array  | 是  | 数据系列（至少1个）                           |

### 3.5 饼图 (PIE_CHART)

```json
{
  "type": "PIE_CHART",
  "title": "区域成交分布",
  "series": [
    {
      "name": "浦东新区",
      "value": 572
    },
    {
      "name": "闵行区",
      "value": 305
    }
  ]
}
```

| 字段      | 类型     | 必填 | 说明             |
|---------|--------|----|----------------|
| type    | string | 是  | 固定值`PIE_CHART` |
| title   | string | 是  | 标题             |
| series  | array  | 是  | 数据系列（至少1个）     |
| ∟ name  | string | 是  | 数据名称           |
| ∟ value | number | 是  | 数据值            |

### 3.6 房产卡片 (PROPERTY_CARD)

```json
{
  "title": "稀缺洋房",
  "type": "PROPERTY_CARD",
  "name": "高兴花园",
  "layout": "1室1厅1卫",
  "area": "47m²",
  "floor": "6/6层",
  "location": "梅陇镇",
  "price": "235万",
  "unit_price": "50,000元/m²",
  "tags": [
    "唯一",
    "七日热门"
  ]
}
```

| 字段         | 类型     | 必填 | 说明                 |
|------------|--------|----|--------------------|
| type       | string | 是  | 固定值`PROPERTY_CARD` |
| title      | string | 否  | 标题                 |
| name       | string | 是  | 房产名称               |
| layout     | string | 是  | 户型描述               |
| area       | string | 是  | 建筑面积               |
| floor      | string | 是  | 楼层信息               |
| location   | string | 是  | 地理位置               |
| price      | string | 是  | 总价                 |
| unit_price | string | 是  | 单价                 |
| tags       | array  | 否  | 特色标签数组             |

---

## 4. 完整应答示例

### 4.1 成功响应 (HTTP 200)

```json
{
  "code": 200,
  "message": "文档生成成功",
  "document": {
    "title": "2025年上海房产政策解读",
    "subtitle": "购房指南与市场趋势分析",
    "sections": [
      {
        "title": "核心政策摘要",
        "type": "POLICY_OVERVIEW",
        "paragraphs": [
          {
            "title": "限购政策要点",
            "widgets": [
              {
                "type": "TEXT",
                "title": "户籍限制",
                "content": "非沪籍家庭需连续缴纳5年社保，单身人士限购1套"
              },
              {
                "title": "购房资格对照表",
                "type": "TABLE",
                "columns": [
                  "购房群体",
                  "限购套数",
                  "社保要求"
                ],
                "rows": [
                  {
                    "cells": {
                      "购房群体": {
                        "type": "TEXT",
                        "value": "沪籍单身"
                      },
                      "限购套数": {
                        "type": "TEXT",
                        "value": "2套"
                      },
                      "社保要求": {
                        "type": "TEXT",
                        "value": "无"
                      }
                    }
                  },
                  {
                    "cells": {
                      "购房群体": {
                        "type": "TEXT",
                        "value": "非沪籍家庭"
                      },
                      "限购套数": {
                        "type": "TEXT",
                        "value": "1套",
                        "recommended": true
                      },
                      "社保要求": {
                        "type": "PROGRESS_BAR",
                        "value": 100
                      }
                    }
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "title": "交易税费对比",
        "type": "TAX_COMPARISON",
        "paragraphs": [
          {
            "widgets": [
              {
                "type": "BAR_CHART",
                "title": "普通住宅 vs 非普通住宅税费",
                "x_axis": [
                  "契税",
                  "增值税",
                  "个人所得税"
                ],
                "series": [
                  {
                    "name": "普通住宅",
                    "data": [
                      1.5,
                      5.3,
                      1
                    ]
                  },
                  {
                    "name": "非普通住宅",
                    "data": [
                      3,
                      5.6,
                      2
                    ]
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "title": "市场动态",
        "type": "MARKET_DATA",
        "paragraphs": [
          {
            "title": "价格趋势",
            "widgets": [
              {
                "title": "月度成交均价走势",
                "type": "LINE_CHART",
                "x_axis": [
                  "2025-01",
                  "2025-02",
                  "2025-03",
                  "2025-04"
                ],
                "series": [
                  {
                    "name": "浦东",
                    "data": [
                      72000,
                      73500,
                      74200,
                      75100
                    ]
                  },
                  {
                    "name": "浦西",
                    "data": [
                      68500,
                      69200,
                      70300,
                      71200
                    ]
                  }
                ]
              }
            ]
          },
          {
            "title": "区域分布",
            "widgets": [
              {
                "title": "各区成交量占比",
                "type": "PIE_CHART",
                "series": [
                  {
                    "name": "浦东新区",
                    "value": 35
                  },
                  {
                    "name": "闵行区",
                    "value": 22
                  },
                  {
                    "name": "徐汇区",
                    "value": 18
                  },
                  {
                    "name": "静安区",
                    "value": 15
                  },
                  {
                    "name": "其他区域",
                    "value": 10
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "title": "推荐房源",
        "type": "HOT_PROPERTIES",
        "paragraphs": [
          {
            "widgets": [
              {
                "title": "精选房源",
                "type": "PROPERTY_CARD",
                "style": "MODERN",
                "data": [
                  {
                    "name": "陆家嘴壹号院",
                    "layout": "3室2厅2卫",
                    "area": "128m²",
                    "floor": "32/45层",
                    "location": "浦东陆家嘴",
                    "price": "1,580万",
                    "unit_price": "123,400元/m²",
                    "tags": [
                      "地铁房",
                      "学区房"
                    ]
                  },
                  {
                    "name": "静安府西区",
                    "layout": "2室1厅1卫",
                    "area": "89m²",
                    "floor": "15/28层",
                    "location": "静安大宁",
                    "price": "950万",
                    "unit_price": "106,700元/m²",
                    "tags": [
                      "满五唯一",
                      "南北通透"
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 4.2 错误响应示例

```json
{
  "code": 404,
  "message": "No matching policy found for region: '北京朝阳' and keyword: '房屋继承税'",
  "data": {
    "suggestion": "Try searching in Shanghai or adjust keyword to '房产继承'"
  }
}
```

```json
{
  "code": 400,
  "message": "Invalid parameter: 'keyword' cannot be empty",
  "data": null
}
```

## 4.3. 状态码说明

| 状态码 | 说明      | 典型场景         |
|-----|---------|--------------|
| 200 | 请求成功    | 正常返回数据       |
| 400 | 请求参数错误  | 地域格式错误/关键字为空 |
| 401 | 认证失败    | 无效/过期的访问令牌   |
| 403 | 权限不足    | 用户无权访问该资源    |
| 404 | 资源不存在   | 无匹配的政策解读内容   |
| 500 | 服务器内部错误 | 数据库连接失败等系统错误 |
| 503 | 服务暂时不可用 | 系统维护或过载      |

---